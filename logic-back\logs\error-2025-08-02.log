2025-08-02 09:57:52.066 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Injector.resolveConstructorParams (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (C:\\Users\\<USER>\\Desktop\\LL\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 09:57:52.125 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.125 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.125 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.125 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.126 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.126 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.126 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.126 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.126 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.127 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.127 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.127 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.128 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.128 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.128 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.129 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.129 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.129 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.130 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 09:57:52.130 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai","stack":[null]}
2025-08-02 10:00:39.106 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2896/stats - Cannot GET /api/user/class/teacher/2896/stats {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
NotFoundException: Cannot GET /api/user/class/teacher/2896/stats
    at callback (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:342:13)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:297:9
    at processParams (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (C:\Users\<USER>\Desktop\LL\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at C:\Users\<USER>\Desktop\LL\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (C:\Users\<USER>\Desktop\LL\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 10:00:39.107 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":25544,"hostname":"zhulezai"}
{"url":"/api/user/class/teacher/2896/stats","method":"GET","statusCode":404,"message":"Cannot GET /api/user/class/teacher/2896/stats","details":{"message":"Cannot GET /api/user/class/teacher/2896/stats","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T02:00:39.107Z"}
